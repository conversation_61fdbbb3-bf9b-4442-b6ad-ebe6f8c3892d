import z from 'zod'

export const step1Schema = z
  .object({
    name: z.string().min(3, 'Esse campo é obrigatório'),
    description: z.string().optional(),
    isMandatory: z.boolean().optional(),
    duration: z.string().optional(),
    startDate: z.string().optional(),
    endDate: z.string().optional(),
  })
  .refine(
    (data) => {
      if (data.isMandatory && data.duration === '4') {
        return data.startDate && data.endDate
      }
      return true
    },
    {
      message: 'Esse campo é obrigatório',
      path: ['startDate'],
    }
  )
