import { Checkbox, Textarea } from '@ads/components-react'
import { Controller, useFormContext } from 'react-hook-form'
import { HiOutlineClipboardDocumentCheck } from 'react-icons/hi2'

import { InputField } from '@/components/ui/input-form'
import { SelectAds } from '@/components/ui/select-ads'

export function Step1() {
  const {
    watch,
    register,
    control,
    formState: { errors },
  } = useFormContext()

  return (
    <div className="min-w-full space-y-3 rounded-lg bg-white p-8">
      <h1 className="flex items-center gap-2 text-ctx-content-title ts-heading-sm">
        <HiOutlineClipboardDocumentCheck />
        Informações Básicas
      </h1>

      <section className="space-y-8 rounded-xl border border-border p-4">
        <InputField
          id="name"
          label="Nome"
          type="text"
          placeholder="Nome da trilha de apendizagem"
          {...register('name')}
          errorMessage={errors.name?.message as string}
        />

        <Textarea
          fullWidth
          label="Descrição"
          placeholder="Descrição da trilha de aprendizagem"
          rows={6}
          {...register('description')}
        />

        <div className="space-y-8">
          <Controller
            name="isMandatory"
            control={control}
            render={({ field }) => (
              <Checkbox
                className="w-[130px]"
                label="Trilha obrigatória"
                checked={field.value}
                onCheckedChange={field.onChange}
              />
            )}
          />
          <div className="flex items-center gap-8">
            {watch('isMandatory') && (
              <Controller
                name="duration"
                control={control}
                render={({ field }) => (
                  <div className="w-[286px]">
                    <SelectAds
                      custom={{
                        trigger: 'h-9',
                      }}
                      options={[
                        { value: '1', label: '1 mês' },
                        { value: '2', label: '3 meses' },
                        { value: '3', label: '6 meses' },
                        { value: '4', label: 'Personalizado' },
                      ]}
                      label="Selecionar duração"
                      placeholder="Carga horária"
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      value={field.value}
                    />
                  </div>
                )}
              />
            )}

            {watch('duration') === '4' && watch('isMandatory') && (
              <div className="flex w-full items-end gap-4">
                <InputField
                  height="h-9"
                  id="startDate"
                  label="Data de início"
                  type="date"
                  errorMessage={errors.startDate?.message as string}
                  {...register('startDate')}
                />
                <span className="text-ctx-content-base ts-paragraph-xxs">
                  até
                </span>
                <InputField
                  height="h-9"
                  id="endDate"
                  label="Data de término"
                  type="date"
                  errorMessage={errors.endDate?.message as string}
                  {...register('endDate')}
                />
              </div>
            )}
          </div>
        </div>
      </section>
    </div>
  )
}
