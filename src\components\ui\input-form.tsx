import { TextField, TextFieldProps } from '@ads/components-react'
import { forwardRef } from 'react'

interface InputFormProps extends TextFieldProps {
  errorMessage?: string
  isAutoComplete?: boolean
  size?: 'sm' | 'md' | 'lg'
  onColor?: boolean
  height?: string
}

export const InputField = forwardRef<HTMLInputElement, InputFormProps>(
  function InputField(
    { id, label, type = 'text', height, errorMessage, leadingIcon, ...rest },
    ref
  ) {
    return (
      <div className="w-full flex-col gap-1.5">
        <TextField
          id={id}
          ref={ref}
          label={label}
          fullWidth
          type={type}
          hasError={!!errorMessage}
          leadingIcon={leadingIcon}
          custom={{
            input: height || 'h-11',
          }}
          {...rest}
        />
        <span className="text-red-600 ts-subtitle-xxxs">{errorMessage}</span>
      </div>
    )
  }
)
